<script lang="ts">
	import { onMount } from 'svelte';
	import { goto } from '$app/navigation';
	import { userStore } from '$lib/stores/userStore.svelte';

	let mounted = $state(false);
	let videoElement = $state<HTMLVideoElement>();
	let canvasElement = $state<HTMLCanvasElement>();
	let stream: MediaStream | null = null;
	let capturedPhoto = $state<string | null>(null);
	let isCapturing = $state(false);
	let cameraError = $state<string | null>(null);
	let countdownActive = $state(false);
	let countdownNumber = $state<number | null>(null);
	let showFlash = $state(false);

	async function initializeCamera() {
		try {
			// Request camera access
			stream = await navigator.mediaDevices.getUserMedia({
				video: {
					width: { ideal: 1080 },
					height: { ideal: 1920 },
					facingMode: 'user' // Front camera
				},
				audio: false
			});

			if (videoElement) {
				videoElement.srcObject = stream;
				console.log('Camera stream set successfully');
			} else {
				console.log('Video element not found');
			}
		} catch (error) {
			console.error('Error accessing camera:', error);
			cameraError = 'Unable to access camera. Please ensure camera permissions are granted.';
		}
	}

	function startCountdown() {
		if (countdownActive) return; // Prevent multiple countdowns

		countdownActive = true;
		countdownNumber = 3;

		const countdownInterval = setInterval(() => {
			if (countdownNumber && countdownNumber > 1) {
				countdownNumber = countdownNumber - 1;
			} else {
				clearInterval(countdownInterval);
				countdownNumber = null;

				// Show flash effect
				showFlash = true;
				setTimeout(() => {
					showFlash = false;
					actuallyTakePhoto();
					countdownActive = false;
				}, 200); // Flash duration
			}
		}, 1000); // 1 second intervals
	}

	function actuallyTakePhoto() {
		console.log('Actually taking photo');
		if (!videoElement || !canvasElement) {
			console.log('Video or canvas element missing');
			return;
		}

		isCapturing = true;

		// Set canvas size to match video
		const canvas = canvasElement;
		const video = videoElement;

		canvas.width = video.videoWidth;
		canvas.height = video.videoHeight;

		// Draw video frame to canvas with mirror correction
		const ctx = canvas.getContext('2d');
		if (ctx) {
			// Flip the canvas horizontally to correct the mirror effect
			ctx.scale(-1, 1);
			ctx.drawImage(video, -canvas.width, 0);

			// Convert to base64
			const photoData = canvas.toDataURL('image/jpeg', 0.8);
			capturedPhoto = photoData;

			// Store in global state
			userStore.setPhoto(photoData);
		}

		setTimeout(() => {
			isCapturing = false;
		}, 300);
	}

	function capturePhoto() {
		console.log('Capture photo clicked - starting countdown');
		if (!videoElement || !canvasElement) {
			console.log('Video or canvas element missing');
			return;
		}
		startCountdown();
	}

	function retakePhoto() {
		capturedPhoto = null;
		userStore.setPhoto('');
		// Restart camera stream
		initializeCamera();
	}

	function confirmPhoto() {
		if (capturedPhoto) {
			// Navigate to next page (for now, go back to home)
			goto('/');
		}
	}

	function handleBack() {
		goto('/character');
	}

	onMount(() => {
		mounted = true;
		
		// Check if user has selected character
		if (!userStore.data.character) {
			goto('/character');
			return;
		}
		
		initializeCamera();
		
		// Cleanup on unmount
		return () => {
			if (stream) {
				stream.getTracks().forEach(track => track.stop());
			}
		};
	});
</script>

<!-- Full-screen background -->
<div class="relative h-screen w-full overflow-hidden">
	<!-- Gradient Background -->
	<div class="absolute inset-0 bg-gradient-to-br from-indigo-900 via-purple-900 to-pink-900"></div>
	
	<!-- Overlay for better contrast -->
	<div class="absolute inset-0 bg-black/40"></div>

	<!-- Content -->
	<div class="relative z-10 flex h-full flex-col items-center justify-center px-12 text-center">
		<!-- Back Button -->
		<button
			onclick={handleBack}
			class="absolute top-8 left-8 flex items-center gap-2 text-white/80 hover:text-white transition-colors duration-300 text-xl"
			class:animate-slide-up={mounted}
		>
			<svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
				<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"/>
			</svg>
			Back
		</button>

		<!-- Main Content Container -->
		<div class="max-w-4xl space-y-12" class:animate-fade-in={mounted}>
			<!-- Title -->
			<div class="space-y-6" class:animate-slide-up={mounted} style="animation-delay: 0.2s;">
				<h1 class="text-6xl md:text-7xl lg:text-8xl font-bold text-white drop-shadow-lg leading-tight">
					Take Your Photo
				</h1>
				<p class="text-2xl md:text-3xl text-white/90 drop-shadow-md font-light">
					Position yourself in the frame and capture your photo
				</p>
			</div>

			<!-- Camera/Photo Section -->
			<div class="relative" class:animate-slide-up={mounted} style="animation-delay: 0.4s;">
				{#if cameraError}
					<!-- Camera Error -->
					<div class="bg-red-500/20 border border-red-500/50 rounded-3xl p-8 text-white">
						<div class="text-6xl mb-4">📷</div>
						<h3 class="text-2xl font-semibold mb-4">Camera Access Required</h3>
						<p class="text-lg mb-6">{cameraError}</p>
						<button
							onclick={initializeCamera}
							class="px-8 py-4 bg-red-500 hover:bg-red-600 rounded-full text-white font-semibold transition-colors duration-300"
						>
							Try Again
						</button>
					</div>
				{:else}
					<!-- Camera Preview / Captured Photo with Border Frame -->
					<div class="relative w-[600px] h-[500px] md:w-[700px] md:h-[600px] lg:w-[800px] lg:h-[700px] mx-auto bg-black rounded-3xl overflow-hidden shadow-2xl border-4 border-white/30 backdrop-blur-sm">
								{#if capturedPhoto}
									<!-- Show captured photo -->
									<img
										src={capturedPhoto}
										alt="User's captured selfie"
										class="w-full h-full object-cover"
									/>

									<!-- Photo overlay controls -->
									<div class="absolute inset-0 bg-black/20 flex items-end justify-center pb-8">
										<div class="flex gap-4">
											<button
												onclick={retakePhoto}
												class="px-6 py-3 bg-white/20 backdrop-blur-sm border border-white/30 text-white rounded-full hover:bg-white/30 transition-all duration-300"
											>
												Retake
											</button>
											<button
												onclick={confirmPhoto}
												class="px-8 py-3 bg-gradient-to-r from-blue-500 to-purple-600 text-white rounded-full hover:scale-105 transition-all duration-300 font-semibold"
											>
												Use This Photo
											</button>
										</div>
									</div>
								{:else}
									<!-- Camera preview with mirror effect -->
									<video
										bind:this={videoElement}
										autoplay
										playsinline
										muted
										class="w-full h-full object-cover -scale-x-100"
									></video>

									<!-- Camera overlay -->
									<div class="absolute inset-0 flex flex-col">
										<!-- Face guide overlay -->
										<div class="flex-1 flex items-center justify-center">
											<div class="w-64 h-64 md:w-80 md:h-80 border-4 border-white/50 rounded-full border-dashed animate-pulse"></div>
										</div>
									</div>

									<!-- Countdown Overlay -->
									{#if countdownActive && countdownNumber}
										<div class="absolute inset-0 bg-black/50 flex items-center justify-center z-60">
											<div class="text-white text-9xl md:text-[12rem] font-bold animate-ping">
												{countdownNumber}
											</div>
										</div>
									{/if}

									<!-- Flash Effect -->
									{#if showFlash}
										<div class="absolute inset-0 bg-white z-70 animate-pulse"></div>
									{/if}
								{/if}
					</div>

					<!-- Capture button positioned outside the camera container -->
					{#if !capturedPhoto && !cameraError}
						<div class="mt-8 flex justify-center">
							<button
								onclick={capturePhoto}
								class="w-24 h-24 md:w-28 md:h-28 bg-white rounded-full shadow-2xl hover:scale-110 transition-all duration-300 flex items-center justify-center border-4 border-blue-500"
								class:animate-pulse={isCapturing || countdownActive}
								class:opacity-50={countdownActive}
								disabled={isCapturing || countdownActive}
								aria-label="Capture photo"
							>
								<div class="w-16 h-16 md:w-20 md:h-20 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full shadow-lg"></div>
							</button>
						</div>
					{/if}
				{/if}
			</div>

			<!-- Instructions -->
			{#if !capturedPhoto && !cameraError}
				<div class="text-center" class:animate-slide-up={mounted} style="animation-delay: 0.6s;">
					<p class="text-lg text-white/80">
						• Position your face in the circle<br>
						• Look directly at the camera<br>
						• Tap the capture button when ready
					</p>
				</div>
			{/if}
		</div>
	</div>
</div>

<!-- Hidden canvas for photo capture -->
<canvas bind:this={canvasElement} class="hidden"></canvas>
