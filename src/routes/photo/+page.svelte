<script lang="ts">
	import { onMount } from 'svelte';
	import { goto } from '$app/navigation';
	import { userStore } from '$lib/stores/userStore.svelte';
	import { PUBLIC_API_KEY } from '$env/static/public';

	let mounted = $state(false);
	let videoElement = $state<HTMLVideoElement>();
	let canvasElement = $state<HTMLCanvasElement>();
	let stream: MediaStream | null = null;
	let capturedPhoto = $state<string | null>(null);
	let capturedPhotoFile = $state<File | null>(null);
	let isCapturing = $state(false);
	let cameraError = $state<string | null>(null);
	let countdownActive = $state(false);
	let countdownNumber = $state<number | null>(null);
	let showFlash = $state(false);
	let isProcessing = $state(false);
	let processingMessage = $state('Processing your photo...');
	let faceSwappedImage = $state<string | null>(null);

	async function initializeCamera() {
		try {
			// Request camera access
			stream = await navigator.mediaDevices.getUserMedia({
				video: {
					width: { ideal: 1080 },
					height: { ideal: 1920 },
					facingMode: 'user' // Front camera
				},
				audio: false
			});

			if (videoElement) {
				videoElement.srcObject = stream;
				console.log('Camera stream set successfully');
			} else {
				console.log('Video element not found');
			}
		} catch (error) {
			console.error('Error accessing camera:', error);
			cameraError = 'Unable to access camera. Please ensure camera permissions are granted.';
		}
	}

	function startCountdown() {
		if (countdownActive) return; // Prevent multiple countdowns

		countdownActive = true;
		countdownNumber = 3;

		const countdownInterval = setInterval(() => {
			if (countdownNumber && countdownNumber > 1) {
				countdownNumber = countdownNumber - 1;
			} else {
				clearInterval(countdownInterval);
				countdownNumber = null;

				// Show flash effect
				showFlash = true;
				setTimeout(() => {
					showFlash = false;
					actuallyTakePhoto();
					countdownActive = false;
				}, 200); // Flash duration
			}
		}, 1000); // 1 second intervals
	}

	function actuallyTakePhoto() {
		console.log('Actually taking photo');
		if (!videoElement || !canvasElement) {
			console.log('Video or canvas element missing');
			return;
		}

		isCapturing = true;

		// Set canvas size to match video
		const canvas = canvasElement;
		const video = videoElement;

		canvas.width = video.videoWidth;
		canvas.height = video.videoHeight;

		// Draw video frame to canvas with mirror correction
		const ctx = canvas.getContext('2d');
		if (ctx) {
			// Flip the canvas horizontally to correct the mirror effect
			ctx.scale(-1, 1);
			ctx.drawImage(video, -canvas.width, 0);

			// Convert canvas to blob for File creation
			canvas.toBlob((blob) => {
				if (blob) {
					// Create File object for form submission
					const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
					const fileName = `photo-${timestamp}.jpg`;
					capturedPhotoFile = new File([blob], fileName, { type: 'image/jpeg' });

					// Create display URL for preview
					capturedPhoto = URL.createObjectURL(blob);

					// Store File object in global state
					userStore.setPhoto(capturedPhotoFile);

					console.log('Photo captured as File:', {
						name: fileName,
						size: blob.size,
						type: blob.type
					});
				}
			}, 'image/jpeg', 0.8);
		}

		setTimeout(() => {
			isCapturing = false;
		}, 300);
	}

	function capturePhoto() {
		console.log('Capture photo clicked - starting countdown');
		if (!videoElement || !canvasElement) {
			console.log('Video or canvas element missing');
			return;
		}
		startCountdown();
	}

	function retakePhoto() {
		// Clean up the blob URL to prevent memory leaks
		if (capturedPhoto) {
			URL.revokeObjectURL(capturedPhoto);
		}

		capturedPhoto = null;
		capturedPhotoFile = null;
		userStore.setPhoto(null);
		// Restart camera stream
		initializeCamera();
	}

	async function submitForFaceSwap() {
		if (!capturedPhotoFile) return;

		isProcessing = true;
		processingMessage = 'Preparing your photo...';

		try {
			// Prepare the data for submission using FormData
			const userData = userStore.data;
			const formData = new FormData();

			// Add the photo file
			formData.append('photo', capturedPhotoFile);

			// Add other user data
			if (userData.gender) formData.append('gender', userData.gender);
			if (userData.character) formData.append('character', userData.character);
			if (userData.characterIndex) formData.append('characterIndex', userData.characterIndex.toString());
			if (userData.name) formData.append('name', userData.name);
			formData.append('timestamp', new Date().toISOString());

			console.log('Submitting FormData for face swap:', {
				photoFile: capturedPhotoFile.name,
				photoSize: capturedPhotoFile.size,
				gender: userData.gender,
				character: userData.character,
				characterIndex: userData.characterIndex
			});

			processingMessage = 'Creating your character...';

			if (!userData.gender || !userData.characterIndex) {
				throw new Error('Missing user data');
			}

			// Update FormData to use the correct file and field names
			formData.set('captured_file', capturedPhotoFile);
			formData.set('gender', userData.gender);
			formData.set('template_idx', userData.characterIndex.toString());

			// TODO: Replace with actual API call
			const response = await fetch('http://localhost:8000/face-swapper', {
				method: 'POST',
				headers: { 'x-api-key': PUBLIC_API_KEY },
				body: formData
			});
			const result: {
				status: 'success' | 'error';
				message: string;
				session: string;
			} = await response.json();
			faceSwappedImage = `http://localhost:8000/static/${result.session}-${userData.characterIndex}.png`;

			processingMessage = 'Complete! Redirecting...';

			// // Wait a bit more then navigate
			// setTimeout(() => {
			// 	isProcessing = false;
			// 	goto('/'); // Navigate to results or next page
			// }, 1500);
		} catch (error) {
			console.error('Face swap submission failed:', error);
			processingMessage = 'Something went wrong. Please try again.';

			// Reset after error
			setTimeout(() => {
				isProcessing = false;
			}, 3000);
		}
	}

	function confirmPhoto() {
		if (capturedPhoto) {
			submitForFaceSwap();
		}
	}

	function handleBack() {
		goto('/character');
	}

	onMount(() => {
		mounted = true;

		// Check if user has selected character
		if (!userStore.data.character) {
			goto('/character');
			return;
		}

		initializeCamera();

		// Cleanup on unmount
		return () => {
			if (stream) {
				stream.getTracks().forEach((track) => track.stop());
			}
		};
	});
</script>

<!-- Full-screen background -->
<div class="relative h-screen w-full overflow-hidden">
	<!-- Gradient Background -->
	<div class="absolute inset-0 bg-gradient-to-br from-indigo-900 via-purple-900 to-pink-900"></div>

	<!-- Overlay for better contrast -->
	<div class="absolute inset-0 bg-black/40"></div>

	<!-- Content -->
	<div class="relative z-10 flex h-full flex-col items-center justify-center px-12 text-center">
		<!-- Back Button -->
		<button
			onclick={handleBack}
			class="absolute top-8 left-8 flex items-center gap-2 text-xl text-white/80 transition-colors duration-300 hover:text-white"
			class:animate-slide-up={mounted}
		>
			<svg class="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
				<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
			</svg>
			Back
		</button>

		<!-- Main Content Container -->
		<div class="max-w-4xl space-y-12" class:animate-fade-in={mounted}>
			{#if isProcessing}
				<!-- Processing/Waiting Screen -->
				<div class="flex flex-col items-center justify-center space-y-8">
					<!-- Processing Title -->
					<div class="space-y-4 text-center">
						<h1
							class="text-5xl leading-tight font-bold text-white drop-shadow-lg md:text-6xl lg:text-7xl"
						>
							Creating Your Character
						</h1>
						<p class="text-xl font-light text-white/90 drop-shadow-md md:text-2xl">
							{processingMessage}
						</p>
					</div>

					<!-- Video Container -->
					<div
						class="relative mx-auto h-[400px] w-[600px] overflow-hidden rounded-3xl border-4 border-white/30 shadow-2xl backdrop-blur-sm md:h-[500px] md:w-[800px] lg:h-[600px] lg:w-[900px]"
					>
						<video autoplay loop muted playsinline class="h-full w-full object-cover">
							<source src="/homepage.mp4" type="video/mp4" />
							<div class="flex h-full items-center justify-center bg-black/50 text-white">
								<div class="text-center">
									<div class="mb-4 text-6xl">🎬</div>
									<p class="text-xl">Loading preview...</p>
								</div>
							</div>
						</video>

						<!-- Processing Overlay -->
						<div class="absolute inset-0 flex items-end justify-center bg-black/20 pb-8">
							<div
								class="flex items-center gap-4 rounded-full border border-white/20 bg-white/10 px-6 py-3 backdrop-blur-sm"
							>
								<div
									class="h-6 w-6 animate-spin rounded-full border-2 border-white/30 border-t-white"
								></div>
								<span class="font-medium text-white">Processing...</span>
							</div>
						</div>
					</div>

					<!-- Progress Indicator -->
					<div class="text-center text-white/70">
						<p class="text-lg">This may take a few moments</p>
						<div class="mt-4 flex justify-center">
							<div class="flex space-x-2">
								<div class="h-3 w-3 animate-pulse rounded-full bg-white/50"></div>
								<div
									class="h-3 w-3 animate-pulse rounded-full bg-white/50"
									style="animation-delay: 0.2s;"
								></div>
								<div
									class="h-3 w-3 animate-pulse rounded-full bg-white/50"
									style="animation-delay: 0.4s;"
								></div>
							</div>
						</div>
					</div>
				</div>
			{:else}
				<!-- Title -->
				<div class="space-y-6" class:animate-slide-up={mounted} style="animation-delay: 0.2s;">
					<h1
						class="text-6xl leading-tight font-bold text-white drop-shadow-lg md:text-7xl lg:text-8xl"
					>
						Take Your Photo
					</h1>
					<p class="text-2xl font-light text-white/90 drop-shadow-md md:text-3xl">
						Position yourself in the frame and capture your photo
					</p>
				</div>
			{/if}

			{#if !isProcessing}
				<!-- Camera/Photo Section -->
				<div class="relative" class:animate-slide-up={mounted} style="animation-delay: 0.4s;">
					{#if cameraError}
						<!-- Camera Error -->
						<div class="rounded-3xl border border-red-500/50 bg-red-500/20 p-8 text-white">
							<div class="mb-4 text-6xl">📷</div>
							<h3 class="mb-4 text-2xl font-semibold">Camera Access Required</h3>
							<p class="mb-6 text-lg">{cameraError}</p>
							<button
								onclick={initializeCamera}
								class="rounded-full bg-red-500 px-8 py-4 font-semibold text-white transition-colors duration-300 hover:bg-red-600"
							>
								Try Again
							</button>
						</div>
					{:else}
						<!-- Camera Preview / Captured Photo with Border Frame -->
						<div
							class="relative mx-auto h-[500px] w-[600px] overflow-hidden rounded-3xl border-4 border-white/30 bg-black shadow-2xl backdrop-blur-sm md:h-[600px] md:w-[700px] lg:h-[700px] lg:w-[800px]"
						>
							{#if capturedPhoto}
								<!-- Show captured photo -->
								<img
									src={capturedPhoto}
									alt="User's captured selfie"
									class="h-full w-full object-cover"
								/>

								<!-- Photo overlay controls -->
								<div class="absolute inset-0 flex items-end justify-center pb-8">
									<div class="flex gap-4">
										<button
											onclick={retakePhoto}
											class="rounded-full border border-white/30 bg-white/20 px-6 py-3 text-white backdrop-blur-sm transition-all duration-300 hover:bg-white/30"
										>
											Retake
										</button>
										<button
											onclick={confirmPhoto}
											class="rounded-full bg-gradient-to-r from-blue-500 to-purple-600 px-8 py-3 font-semibold text-white transition-all duration-300 hover:scale-105"
										>
											Use This Photo
										</button>
									</div>
								</div>
							{:else}
								<!-- Camera preview with mirror effect -->
								<video
									bind:this={videoElement}
									autoplay
									playsinline
									muted
									class="h-full w-full -scale-x-100 object-cover"
								></video>

								<!-- Camera overlay -->
								<div class="absolute inset-0 flex flex-col">
									<!-- Face guide overlay -->
									<div class="flex flex-1 items-center justify-center">
										<div
											class="h-64 w-64 animate-pulse rounded-full border-4 border-dashed border-white/50 md:h-80 md:w-80"
										></div>
									</div>
								</div>

								<!-- Countdown Overlay -->
								{#if countdownActive && countdownNumber}
									<div class="absolute inset-0 z-60 flex items-center justify-center bg-black/50">
										<div class="animate-ping text-9xl font-bold text-white md:text-[12rem]">
											{countdownNumber}
										</div>
									</div>
								{/if}

								<!-- Flash Effect -->
								{#if showFlash}
									<div class="absolute inset-0 z-70 animate-pulse bg-white"></div>
								{/if}
							{/if}
						</div>

						<!-- Capture button positioned outside the camera container -->
						{#if !capturedPhoto && !cameraError}
							<div class="mt-8 flex justify-center">
								<button
									onclick={capturePhoto}
									class="flex h-24 w-24 items-center justify-center rounded-full border-4 border-blue-500 bg-white shadow-2xl transition-all duration-300 hover:scale-110 md:h-28 md:w-28"
									class:animate-pulse={isCapturing || countdownActive}
									class:opacity-50={countdownActive}
									disabled={isCapturing || countdownActive}
									aria-label="Capture photo"
								>
									<div
										class="h-16 w-16 rounded-full bg-gradient-to-r from-blue-500 to-purple-600 shadow-lg md:h-20 md:w-20"
									></div>
								</button>
							</div>
						{/if}
					{/if}
				</div>

				<!-- Instructions -->
				{#if !capturedPhoto && !cameraError}
					<div class="text-center" class:animate-slide-up={mounted} style="animation-delay: 0.6s;">
						<p class="text-lg text-white/80">
							• Position your face in the circle<br />
							• Look directly at the camera<br />
							• Tap the capture button when ready
						</p>
					</div>
				{/if}
			{/if}
		</div>
	</div>
</div>

<!-- Hidden canvas for photo capture -->
<canvas bind:this={canvasElement} class="hidden"></canvas>
