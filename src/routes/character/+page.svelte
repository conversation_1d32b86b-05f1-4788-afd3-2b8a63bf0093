<script lang="ts">
	import { onMount } from 'svelte';
	import { goto } from '$app/navigation';
	import { userStore } from '$lib/stores/userStore.svelte';

	let mounted = $state(false);
	let selectedCharacter = $state<string | null>(null);
	let currentIndex = $state(0);
	let carouselContainer: HTMLDivElement;
	let isDragging = $state(false);
	let startX = 0;
	let currentX = 0;
	let translateX = $state(0);

	// Get characters based on selected gender
	let availableCharacters = $derived.by(() => {
		const gender = userStore.data.gender;
		if (!gender) {
			// If no gender selected, redirect back to gender selection
			goto('/gender');
			return [];
		}

		const characters = [];
		for (let i = 1; i <= 5; i++) {
			characters.push({
				id: `${gender}-${i}`,
				name: `Character ${i}`,
				image: `/characters/${gender}-${i}.png`
			});
		}
		return characters;
	});

	function handleCharacterSelect() {
		if (selectedCharacter) {
			userStore.setCharacter(selectedCharacter);
			userStore.setCharacterIndex(currentIndex); // Store 1-based index for backend
			// Navigate to photo capture page
			setTimeout(() => {
				goto('/photo');
			}, 800);
		}
	}

	function handleBack() {
		goto('/gender');
	}

	// Touch/Mouse event handlers for carousel with infinite loop
	function handleStart(event: TouchEvent | MouseEvent) {
		isDragging = true;
		startX = 'touches' in event ? event.touches[0].clientX : event.clientX;
		currentX = startX;
	}

	function handleMove(event: TouchEvent | MouseEvent) {
		if (!isDragging) return;

		event.preventDefault();
		currentX = 'touches' in event ? event.touches[0].clientX : event.clientX;
		const deltaX = currentX - startX;
		translateX = deltaX;
	}

	function handleEnd() {
		if (!isDragging) return;
		isDragging = false;

		const deltaX = currentX - startX;
		const threshold = 80; // Minimum swipe distance

		if (Math.abs(deltaX) > threshold) {
			if (deltaX > 0) {
				// Swipe right - go to previous (with infinite loop)
				currentIndex = currentIndex === 0 ? availableCharacters.length - 1 : currentIndex - 1;
			} else {
				// Swipe left - go to next (with infinite loop)
				currentIndex = currentIndex === availableCharacters.length - 1 ? 0 : currentIndex + 1;
			}
		}

		// Reset transform with smooth animation
		translateX = 0;

		// Update selected character
		selectedCharacter = availableCharacters[currentIndex]?.id || null;
	}

	onMount(() => {
		mounted = true;
		// Set initial character if available
		if (availableCharacters.length > 0) {
			selectedCharacter = availableCharacters[0].id;
		}
	});
</script>

<!-- Full-screen background -->
<div class="relative h-screen w-full overflow-hidden">
	<!-- Gradient Background -->
	<div class="absolute inset-0 bg-gradient-to-br from-indigo-900 via-purple-900 to-pink-900"></div>

	<!-- Animated background elements -->
	<div class="absolute inset-0">
		<div
			class="absolute top-1/4 left-1/4 h-64 w-64 animate-pulse rounded-full bg-blue-500/10 blur-3xl"
		></div>
		<div
			class="absolute right-1/4 bottom-1/4 h-96 w-96 animate-pulse rounded-full bg-purple-500/10 blur-3xl"
			style="animation-delay: 1s;"
		></div>
	</div>

	<!-- Overlay for better contrast -->
	<div class="absolute inset-0 bg-black/30"></div>

	<!-- Content -->
	<div class="relative z-10 flex h-full flex-col items-center justify-center px-12 text-center">
		<!-- Back Button -->
		<button
			onclick={handleBack}
			class="absolute top-8 left-8 flex items-center gap-2 text-xl text-white/80 transition-colors duration-300 hover:text-white"
			class:animate-slide-up={mounted}
		>
			<svg class="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
				<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
			</svg>
			Back
		</button>

		<!-- Main Content Container -->
		<div class="max-w-6xl space-y-12" class:animate-fade-in={mounted}>
			<!-- Title -->
			<div class="space-y-6" class:animate-slide-up={mounted} style="animation-delay: 0.2s;">
				<h1
					class="text-6xl leading-tight font-bold text-white drop-shadow-lg md:text-7xl lg:text-8xl"
				>
					Choose Your Character
				</h1>
				<p class="text-2xl font-light text-white/90 drop-shadow-md md:text-3xl">
					Select your avatar for the experience
				</p>
			</div>

			<!-- Character Carousel -->
			<div class="relative" class:animate-slide-up={mounted} style="animation-delay: 0.4s;">
				<!-- Frame Background -->
				<div
					class="character-frame relative mx-auto h-[800px] w-[800px] md:h-[900px] md:w-[900px] lg:h-[1000px] lg:w-[1000px]"
				>
					<img
						src="/frame.png"
						alt="Character Frame"
						class="pointer-events-none absolute inset-0 z-5 h-full w-full object-contain"
					/>

					<!-- Carousel Container -->
					<div
						bind:this={carouselContainer}
						class="character-carousel absolute inset-0 z-10 flex cursor-grab items-center justify-center overflow-hidden outline-0 active:cursor-grabbing"
						class:cursor-grabbing={isDragging}
						class:dragging={isDragging}
						role="button"
						tabindex="0"
						aria-label="Swipe to navigate characters"
						ontouchstart={handleStart}
						ontouchmove={handleMove}
						ontouchend={handleEnd}
						onmousedown={handleStart}
						onmousemove={handleMove}
						onmouseup={handleEnd}
						onmouseleave={handleEnd}
					>
						<!-- Character Images with smooth horizontal animation -->
						{#each availableCharacters as character, index}
							{#if index === currentIndex || (isDragging && (index === (currentIndex + 1) % availableCharacters.length || index === (currentIndex - 1 + availableCharacters.length) % availableCharacters.length))}
								<div class="absolute top-40 -left-54 inset-0 flex items-center justify-center">
									<div
										class="absolute transition-all duration-500 ease-out"
										style="
										transform: translateX({isDragging
											? index === currentIndex
												? translateX
												: index === (currentIndex + 1) % availableCharacters.length
													? translateX + 800
													: translateX - 800
											: index === currentIndex
												? 0
												: index === (currentIndex + 1) % availableCharacters.length
													? 800
													: -800}px) scale(1);
										opacity: {index === currentIndex ? 1 : isDragging ? 0.7 : 0};
										z-index: {index === currentIndex ? 5 : 3};
									"
									>
										<!-- Character positioning wrapper -->
										<img
											src={character.image}
											alt={character.name}
											class="character-image h-[610px] w-[610px] object-contain"
											draggable="false"
										/>
									</div>
								</div>
							{/if}
						{/each}
					</div>
				</div>
			</div>

			<!-- Select Button -->
			{#if selectedCharacter}
				<div
					class="flex justify-center"
					class:animate-slide-up={mounted}
					style="animation-delay: 0.6s;"
				>
					<button
						onclick={handleCharacterSelect}
						class="hover:shadow-3xl rounded-full bg-gradient-to-r from-blue-500 via-purple-500 to-pink-500 px-16 py-6 text-2xl font-semibold text-white shadow-2xl transition-all duration-300 hover:scale-105 focus:ring-4 focus:ring-white/30 focus:outline-none active:scale-95 md:text-3xl"
					>
						Select Character
					</button>
				</div>
			{/if}
		</div>
	</div>
</div>
