<script lang="ts">
	import { onMount } from 'svelte';
	import { goto } from '$app/navigation';
	import { page } from '$app/stores';
	import { userStore } from '$lib/stores/userStore.svelte';

	let mounted = $state(false);
	let faceSwappedImage = $state<string | null>(null);
	let isPrinting = $state(false);
	let printComplete = $state(false);

	// 4R print dimensions (in mm): 102 x 152 mm (4" x 6")
	const PRINT_WIDTH_MM = 102;
	const PRINT_HEIGHT_MM = 152;
	const PRINT_DPI = 300; // High quality printing

	onMount(() => {
		mounted = true;

		// Get the face-swapped image from URL params or store
		const imageUrl = $page.url.searchParams.get('image');
		if (imageUrl) {
			faceSwappedImage = decodeURIComponent(imageUrl);
		} else {
			// If no image provided, redirect back to photo page
			goto('/photo');
			return;
		}
	});

	async function handlePrint() {
		if (!faceSwappedImage) return;

		isPrinting = true;

		try {
			// Create a new window for printing with 4R dimensions
			const printWindow = window.open('', '_blank', 'width=800,height=600');
			if (!printWindow) {
				throw new Error('Could not open print window');
			}

			// Calculate print dimensions in pixels (4R = 102x152mm at 300 DPI)
			const printWidthPx = Math.round((PRINT_WIDTH_MM / 25.4) * PRINT_DPI); // ~1205px
			const printHeightPx = Math.round((PRINT_HEIGHT_MM / 25.4) * PRINT_DPI); // ~1795px

			printWindow.document.write(`
				<!DOCTYPE html>
				<html>
				<head>
					<title>Print Photo - 4R Size</title>
					<style>
						@page {
							size: 4in 6in; /* 4R size */
							margin: 0;
						}
						
						* {
							margin: 0;
							padding: 0;
							box-sizing: border-box;
						}
						
						body {
							width: 4in;
							height: 6in;
							display: flex;
							align-items: center;
							justify-content: center;
							background: white;
						}
						
						.print-container {
							width: 100%;
							height: 100%;
							display: flex;
							align-items: center;
							justify-content: center;
						}
						
						.print-image {
							max-width: 100%;
							max-height: 100%;
							object-fit: contain;
							border-radius: 8px;
						}
						
						@media print {
							body {
								-webkit-print-color-adjust: exact;
								print-color-adjust: exact;
							}
						}
					</style>
				</head>
				<body>
					<div class="print-container">
						<img src="${faceSwappedImage}" alt="Face Swapped Photo" class="print-image" />
					</div>
				</body>
				</html>
			`);

			printWindow.document.close();

			// Wait for image to load before printing
			const img = printWindow.document.querySelector('.print-image') as HTMLImageElement;
			if (img) {
				img.onload = () => {
					setTimeout(() => {
						printWindow.print();
						printWindow.close();
						printComplete = true;
						isPrinting = false;
					}, 500);
				};
			}

		} catch (error) {
			console.error('Print failed:', error);
			isPrinting = false;
			alert('Printing failed. Please try again.');
		}
	}

	function handleDownload() {
		if (!faceSwappedImage) return;

		// Create download link
		const link = document.createElement('a');
		link.href = faceSwappedImage;
		link.download = `face-swap-result-${new Date().toISOString().slice(0, 10)}.png`;
		document.body.appendChild(link);
		link.click();
		document.body.removeChild(link);
	}

	function handleStartOver() {
		// Reset user data and go back to start
		userStore.reset();
		goto('/');
	}

	function handleTakeAnother() {
		// Keep user data but go back to photo capture
		goto('/photo');
	}
</script>

<!-- Full-screen background -->
<div class="relative h-screen w-full overflow-hidden">
	<!-- Gradient Background -->
	<div class="absolute inset-0 bg-gradient-to-br from-green-900 via-blue-900 to-purple-900"></div>
	
	<!-- Overlay for better contrast -->
	<div class="absolute inset-0 bg-black/40"></div>

	<!-- Content -->
	<div class="relative z-10 flex h-full flex-col items-center justify-center px-12 text-center">
		<!-- Main Content Container -->
		<div class="max-w-4xl space-y-12" class:animate-fade-in={mounted}>
			<!-- Title -->
			<div class="space-y-6" class:animate-slide-up={mounted} style="animation-delay: 0.2s;">
				<h1 class="text-6xl md:text-7xl lg:text-8xl font-bold text-white drop-shadow-lg leading-tight">
					Your Character
				</h1>
				<p class="text-2xl md:text-3xl text-white/90 drop-shadow-md font-light">
					{#if printComplete}
						Photo printed successfully!
					{:else}
						Ready to print in 4R size (4" × 6")
					{/if}
				</p>
			</div>

			<!-- Result Image -->
			{#if faceSwappedImage}
				<div class="relative" class:animate-slide-up={mounted} style="animation-delay: 0.4s;">
					<div class="relative w-[400px] h-[600px] md:w-[500px] md:h-[750px] lg:w-[600px] lg:h-[900px] mx-auto bg-white rounded-3xl overflow-hidden shadow-2xl border-4 border-white/30 backdrop-blur-sm">
						<!-- 4R Aspect Ratio Container -->
						<div class="w-full h-full flex items-center justify-center p-4">
							<img
								src={faceSwappedImage}
								alt="Your face-swapped character"
								class="max-w-full max-h-full object-contain rounded-2xl shadow-lg"
							/>
						</div>

						<!-- Print Status Overlay -->
						{#if isPrinting}
							<div class="absolute inset-0 bg-black/50 flex items-center justify-center">
								<div class="bg-white/10 backdrop-blur-sm border border-white/20 rounded-full px-6 py-3 flex items-center gap-4">
									<div class="w-6 h-6 border-2 border-white/30 border-t-white rounded-full animate-spin"></div>
									<span class="text-white font-medium">Preparing to print...</span>
								</div>
							</div>
						{/if}
					</div>
				</div>
			{/if}

			<!-- Action Buttons -->
			<div class="flex flex-col md:flex-row gap-6 justify-center items-center" class:animate-slide-up={mounted} style="animation-delay: 0.6s;">
				<!-- Print Button -->
				<button
					onclick={handlePrint}
					disabled={isPrinting || !faceSwappedImage}
					class="px-8 py-4 bg-gradient-to-r from-green-500 to-blue-600 text-white rounded-full hover:scale-105 transition-all duration-300 font-semibold text-xl shadow-2xl disabled:opacity-50 disabled:cursor-not-allowed flex items-center gap-3"
				>
					{#if isPrinting}
						<div class="w-5 h-5 border-2 border-white/30 border-t-white rounded-full animate-spin"></div>
						Printing...
					{:else}
						<svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
							<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 17h2a2 2 0 002-2v-4a2 2 0 00-2-2H5a2 2 0 00-2 2v4a2 2 0 002 2h2m2 4h6a2 2 0 002-2v-4a2 2 0 00-2-2H9a2 2 0 00-2 2v4a2 2 0 002 2zm8-12V5a2 2 0 00-2-2H9a2 2 0 00-2 2v4h10z"/>
						</svg>
						Print Photo (4R)
					{/if}
				</button>

				<!-- Download Button -->
				<button
					onclick={handleDownload}
					disabled={!faceSwappedImage}
					class="px-8 py-4 bg-white/20 backdrop-blur-sm border border-white/30 text-white rounded-full hover:bg-white/30 transition-all duration-300 font-semibold text-xl disabled:opacity-50 disabled:cursor-not-allowed flex items-center gap-3"
				>
					<svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
						<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"/>
					</svg>
					Download
				</button>
			</div>

			<!-- Secondary Actions -->
			<div class="flex flex-col md:flex-row gap-4 justify-center items-center" class:animate-slide-up={mounted} style="animation-delay: 0.8s;">
				<button
					onclick={handleTakeAnother}
					class="px-6 py-3 text-white/80 hover:text-white transition-colors duration-300 text-lg underline"
				>
					Take Another Photo
				</button>
				
				<span class="text-white/50">•</span>
				
				<button
					onclick={handleStartOver}
					class="px-6 py-3 text-white/80 hover:text-white transition-colors duration-300 text-lg underline"
				>
					Start Over
				</button>
			</div>

			<!-- Print Info -->
			{#if !printComplete}
				<div class="text-center text-white/70" class:animate-slide-up={mounted} style="animation-delay: 1s;">
					<p class="text-lg mb-2">📏 Print Size: 4R (4" × 6" / 102 × 152 mm)</p>
					<p class="text-sm">High quality 300 DPI printing</p>
				</div>
			{/if}
		</div>
	</div>
</div>

<style>
	@keyframes fade-in {
		from { opacity: 0; }
		to { opacity: 1; }
	}

	@keyframes slide-up {
		from { 
			opacity: 0; 
			transform: translateY(30px); 
		}
		to { 
			opacity: 1; 
			transform: translateY(0); 
		}
	}

	.animate-fade-in {
		animation: fade-in 0.8s ease-out;
	}

	.animate-slide-up {
		animation: slide-up 0.6s ease-out;
	}
</style>
