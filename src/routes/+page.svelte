<script lang="ts">
	import { onMount } from 'svelte';
	import { goto } from '$app/navigation';
	import { userStore } from '$lib/stores/userStore.svelte';
	import { dev } from '$app/environment';

	let mounted = $state(false);
	let videoLoaded = $state(false);

	// Navigate to gender selection page
	function handleStartNow() {
		console.log('Start Now clicked!');
		goto('/gender');
	}

	// Development keyboard shortcuts
	function handleKeydown(event: KeyboardEvent) {
		if (dev && event.ctrlKey && event.shiftKey && event.key === 'R') {
			event.preventDefault();
			userStore.forceReset();
			console.log('Manual store reset triggered via Ctrl+Shift+R');
		}
	}

	// Reset store when homepage loads
	onMount(() => {
		mounted = true;
		// Reset all user data when returning to homepage
		userStore.reset();
		console.log('Homepage loaded - User store reset');

		// Add keyboard listener for development
		if (dev) {
			window.addEventListener('keydown', handleKeydown);
			console.log('Development mode: Press Ctrl+Shift+R to force reset store');
		}

		// Cleanup
		return () => {
			if (dev) {
				window.removeEventListener('keydown', handleKeydown);
			}
		};
	});

	function handleVideoLoad() {
		videoLoaded = true;
	}


</script>

<!-- Full-screen video background -->
<div class="relative h-screen w-full overflow-hidden">
	<!-- Video Background -->
	<video
		class="absolute inset-0 h-full w-full object-cover transition-opacity duration-1000"
		class:opacity-100={videoLoaded}
		class:opacity-0={!videoLoaded}
		autoplay
		muted
		loop
		playsinline
		onloadeddata={handleVideoLoad}
	>
		<source src="/homepage.mp4" type="video/mp4" />
		<!-- Fallback for browsers that don't support video -->
		<div class="absolute inset-0 bg-gradient-to-br from-blue-900 to-purple-900"></div>
	</video>

	<!-- Loading placeholder while video loads -->
	{#if !videoLoaded}
		<div
			class="video-loading absolute inset-0 bg-gradient-to-br from-blue-900 via-purple-900 to-indigo-900"
		></div>
	{/if}

	<!-- Overlay for better text contrast -->
	<div class="absolute inset-0 bg-black/40"></div>

	<!-- Content Overlay -->
	<div
		class="homepage-container relative z-10 flex h-full flex-col items-center justify-center px-12 text-center"
	>
		<!-- Main Content Container -->
		<div class="max-w-6xl space-y-24" class:animate-fade-in={mounted}>
			<!-- Welcome Text -->
			<div class="space-y-12" class:animate-slide-up={mounted} style="animation-delay: 0.2s;">
				<h1 class="text-8xl md:text-9xl lg:text-[12rem] font-bold text-white drop-shadow-lg leading-tight">Welcome</h1>
				<p class="text-3xl md:text-4xl lg:text-5xl text-white/90 drop-shadow-md font-light leading-relaxed">
					Experience something extraordinary
				</p>
			</div>

			<!-- Start Now Button -->
			<div class:animate-slide-up={mounted} style="animation-delay: 0.4s;">
				<button
					onclick={handleStartNow}
					class="start-button group px-16 py-6 text-3xl md:text-4xl lg:text-5xl hover:shadow-3xl relative overflow-hidden rounded-full bg-gradient-to-r from-blue-500 via-purple-500 to-pink-500 font-semibold text-white shadow-2xl transition-all duration-500 hover:scale-110 focus:ring-4 focus:ring-white/30 focus:outline-none active:scale-95 animate-pulse-glow hover:animate-none"
				>
					<!-- Animated Background Layers -->
					<div
						class="absolute inset-0 bg-gradient-to-r from-purple-600 via-pink-500 to-blue-500 opacity-0 transition-opacity duration-500 group-hover:opacity-100"
					></div>

					<!-- Shimmer Effect -->
					<div
						class="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent -translate-x-full group-hover:translate-x-full transition-transform duration-1000 ease-in-out"
					></div>

					<!-- Pulsing Ring -->
					<div
						class="absolute inset-0 rounded-full border-2 border-white/30 animate-ping"
					></div>

					<!-- Pulsing Border -->
					<div
						class="absolute inset-0 rounded-full border-2 border-gradient animate-pulse-border"
					></div>

					<!-- Button Text -->
					<span class="relative z-20 flex items-center justify-center gap-6 group-hover:scale-105 transition-transform duration-300">
						<span class="animate-bounce-subtle">Start Now</span>
						<!-- Arrow Icon with left-right movement -->
						<svg
							class="h-10 w-10 md:h-12 md:w-12 transition-all duration-500 group-hover:translate-x-2 group-hover:scale-110 animate-arrow-wiggle"
							fill="none"
							stroke="currentColor"
							viewBox="0 0 24 24"
						>
							<path
								stroke-linecap="round"
								stroke-linejoin="round"
								stroke-width="2"
								d="M13 7l5 5m0 0l-5 5m5-5H6"
							/>
						</svg>
					</span>

					<!-- Enhanced Ripple Effects -->
					<div
						class="absolute inset-0 scale-0 rounded-full bg-white/30 transition-transform duration-700 group-active:scale-150 group-active:opacity-0"
					></div>

					<!-- Secondary Ripple -->
					<div
						class="absolute inset-0 scale-0 rounded-full bg-gradient-to-r from-blue-300/20 to-purple-300/20 transition-transform duration-500 delay-100 group-active:scale-125"
					></div>

					<!-- Sparkle Effects -->
					<div class="absolute inset-0 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
						<div class="absolute top-1/4 left-1/4 w-2 h-2 bg-white rounded-full animate-twinkle"></div>
						<div class="absolute top-3/4 right-1/4 w-1 h-1 bg-white rounded-full animate-twinkle" style="animation-delay: 0.5s;"></div>
						<div class="absolute top-1/2 right-1/3 w-1.5 h-1.5 bg-white rounded-full animate-twinkle" style="animation-delay: 1s;"></div>
					</div>
				</button>

				<!-- Optional: Additional CTA text -->
				<div class:animate-slide-up={mounted} style="animation-delay: 0.6s;">
					<p class="animate-pulse text-xl md:text-2xl text-white/70 font-light">Tap to begin your journey</p>
				</div>
			</div>
		</div>
	</div>
</div>
