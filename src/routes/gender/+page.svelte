<script lang="ts">
	import { onMount } from 'svelte';
	import { goto } from '$app/navigation';
	import { userStore, GENDER_OPTIONS, type GenderOption } from '$lib/stores/userStore.svelte';

	let mounted = $state(false);
	let selectedGender = $state<string | null>(null);

	function handleGenderSelect(gender: GenderOption) {
		selectedGender = gender;
		userStore.setGender(gender);

		// Auto-navigate after a short delay to show selection
		setTimeout(() => {
			// Navigate to character selection
			goto('/character');
		}, 800);
	}

	function handleBack() {
		goto('/');
	}

	onMount(() => {
		mounted = true;
		// Pre-select if user already has a gender selected
		selectedGender = userStore.data.gender;
	});
</script>

<!-- Full-screen background -->
<div class="relative h-screen w-full overflow-hidden">
	<!-- Gradient Background (instead of video for this page) -->
	<div class="absolute inset-0 bg-gradient-to-br from-indigo-900 via-purple-900 to-pink-900"></div>

	<!-- Animated background elements -->
	<div class="absolute inset-0">
		<div
			class="absolute top-1/4 left-1/4 h-64 w-64 animate-pulse rounded-full bg-blue-500/10 blur-3xl"
		></div>
		<div
			class="absolute right-1/4 bottom-1/4 h-96 w-96 animate-pulse rounded-full bg-purple-500/10 blur-3xl"
			style="animation-delay: 1s;"
		></div>
		<div
			class="absolute top-3/4 left-1/3 h-48 w-48 animate-pulse rounded-full bg-pink-500/10 blur-3xl"
			style="animation-delay: 2s;"
		></div>
	</div>

	<!-- Overlay for better contrast -->
	<div class="absolute inset-0 bg-black/30"></div>

	<!-- Content -->
	<div class="relative z-10 flex h-full flex-col items-center justify-center px-12 text-center">
		<!-- Back Button -->
		<button
			onclick={handleBack}
			class="absolute top-8 left-8 flex items-center gap-2 text-xl text-white/80 transition-colors duration-300 hover:text-white"
			class:animate-slide-up={mounted}
		>
			<svg class="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
				<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
			</svg>
			Back
		</button>

		<!-- Main Content Container -->
		<div class="max-w-4xl space-y-16" class:animate-fade-in={mounted}>
			<!-- Title -->
			<div class="space-y-6" class:animate-slide-up={mounted} style="animation-delay: 0.2s;">
				<h1
					class="text-6xl leading-tight font-bold text-white drop-shadow-lg md:text-7xl lg:text-8xl"
				>
					Select Your Gender
				</h1>
				<p class="text-2xl font-light text-white/90 drop-shadow-md md:text-3xl">
					Help us personalize your experience
				</p>
			</div>

			<!-- Gender Options Grid (2 columns for Male/Female) -->
			<div
				class="grid grid-cols-1 gap-12 md:grid-cols-2 max-w-4xl mx-auto"
				class:animate-slide-up={mounted}
				style="animation-delay: 0.4s;"
			>
				{#each GENDER_OPTIONS as option}
					<button
						onclick={() => handleGenderSelect(option.value)}
						class="gender-option group relative overflow-hidden rounded-3xl border border-white/20 bg-gradient-to-r from-white/10 to-white/5 p-8 text-white backdrop-blur-sm transition-all duration-500 hover:scale-105 hover:bg-gradient-to-r hover:from-blue-500/20 hover:to-purple-500/20 focus:ring-4 focus:ring-white/30 focus:outline-none"
						class:selected={selectedGender === option.value}
					>
						<!-- Selection indicator -->
						{#if selectedGender === option.value}
							<div
								class="animate-pulse-glow absolute inset-0 rounded-3xl bg-gradient-to-r from-blue-500/30 to-purple-500/30"
							></div>
						{/if}

						<!-- Content -->
						<div class="relative z-10 flex flex-col items-center space-y-4">
							<!-- Icon -->
							<div class="text-6xl transition-transform duration-300 group-hover:scale-110">
								{option.icon}
							</div>

							<!-- Label -->
							<span
								class="text-2xl font-semibold transition-colors duration-300 group-hover:text-white md:text-3xl"
							>
								{option.label}
							</span>
						</div>

						<!-- Hover effect -->
						<div
							class="absolute inset-0 rounded-3xl bg-gradient-to-r from-blue-500/10 to-purple-500/10 opacity-0 transition-opacity duration-300 group-hover:opacity-100"
						></div>

						<!-- Shimmer effect -->
						<div
							class="absolute inset-0 -translate-x-full rounded-3xl bg-gradient-to-r from-transparent via-white/10 to-transparent transition-transform duration-1000 ease-in-out group-hover:translate-x-full"
						></div>
					</button>
				{/each}
			</div>


		</div>
	</div>
</div>

<style>
	.gender-option.selected {
		background: linear-gradient(135deg, rgba(59, 130, 246, 0.3), rgba(147, 51, 234, 0.3));
		border-color: rgba(255, 255, 255, 0.5);
		box-shadow: 0 0 30px rgba(59, 130, 246, 0.4);
	}
</style>
