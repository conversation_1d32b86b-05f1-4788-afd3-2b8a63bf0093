<script lang="ts">
	import { userStore } from '$lib/stores/userStore.svelte';
	import { dev } from '$app/environment';

	// Reactive values from the store using Svelte 5 runes
	let progress = $derived(userStore.progress);
	let currentStep = $derived(userStore.currentStep);
	let showProgress = $derived(userStore.showProgress);

	// Development helper - log when progress changes
	$effect(() => {
		if (dev) {
			console.log('Progress updated:', {
				progress,
				currentStep,
				showProgress,
				userData: userStore.data
			});
		}
	});
</script>

{#if showProgress}
	<div class="fixed bottom-0 left-0 right-0 z-50 bg-black/20 backdrop-blur-sm border-t border-white/10">
		<!-- Progress Bar Container -->
		<div class="relative h-3 bg-black/30">
			<!-- Progress Fill -->
			<div 
				class="absolute top-0 left-0 h-full bg-gradient-to-r from-blue-500 via-purple-500 to-pink-500 transition-all duration-1000 ease-out"
				style="width: {progress}%"
			>
				<!-- Animated shine effect -->
				<div class="absolute inset-0 bg-gradient-to-r from-transparent via-white/30 to-transparent animate-shine"></div>
			</div>
		</div>
		
		<!-- Progress Info -->
		<div class="px-6 py-3 flex items-center justify-between text-white/90">
			<div class="flex items-center space-x-3">
				<div class="text-sm font-medium">
					Step {currentStep.step} of {currentStep.total}
				</div>
				<div class="text-xs text-white/70">
					{currentStep.label}
				</div>
				{#if dev}
					<div class="text-xs text-yellow-400 bg-yellow-400/10 px-2 py-1 rounded">
						DEV
					</div>
				{/if}
			</div>

			<div class="text-sm font-medium">
				{progress}% Complete
			</div>
		</div>
	</div>
{/if}

<style>
	@keyframes shine {
		0% {
			transform: translateX(-100%);
		}
		100% {
			transform: translateX(100%);
		}
	}
	
	.animate-shine {
		animation: shine 2s ease-in-out infinite;
	}
</style>
