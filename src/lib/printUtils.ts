/**
 * Print utility functions for handling photo printing
 */

export interface PrintOptions {
	onPrintStart?: () => void;
	onPrintComplete?: () => void;
	onPrintError?: (error: Error) => void;
}

/**
 * Opens a new window and prints the face-swapped image in 4R format
 * @param faceSwappedImage - The base64 or URL of the image to print
 * @param options - Optional callbacks for print events
 */
export async function printPhoto(faceSwappedImage: string, options: PrintOptions = {}): Promise<void> {
	if (!faceSwappedImage) {
		console.log('No face swapped image available for printing');
		return;
	}

	console.log('Starting print process for image:', faceSwappedImage);
	options.onPrintStart?.();

	try {
		// Create a new window for printing with 4R dimensions
		const printWindow = window.open('', '_blank', 'width=800,height=600,scrollbars=yes,resizable=yes');
		if (!printWindow) {
			throw new Error('Could not open print window - popup might be blocked');
		}

		console.log('Print window opened successfully');

		// Create the HTML content
		const htmlContent = createPrintHTML(faceSwappedImage);

		// Write the content to the print window
		printWindow.document.open();
		printWindow.document.write(htmlContent);
		printWindow.document.close();

		// Reset printing state after a delay
		setTimeout(() => {
			options.onPrintComplete?.();
			console.log('Print process completed');
		}, 2000);

	} catch (error) {
		console.error('Print failed:', error);
		const printError = error instanceof Error ? error : new Error('Unknown print error');
		options.onPrintError?.(printError);
	}
}

/**
 * Creates the HTML content for the print window
 * @param imageUrl - The URL or base64 string of the image to print
 * @returns HTML string for the print window
 */
function createPrintHTML(imageUrl: string): string {
	return `<!DOCTYPE html>
<html>
<head>
	<title>Print Photo - 4R Size</title>
	<style>
		@page {
			size: 4in 6in;
			margin: 0;
		}

		* {
			margin: 0;
			padding: 0;
			box-sizing: border-box;
		}

		body {
			width: 4in;
			height: 6in;
			display: flex;
			align-items: center;
			justify-content: center;
			background: white;
			font-family: Arial, sans-serif;
		}

		.print-container {
			width: 100%;
			height: 100%;
			display: flex;
			align-items: center;
			justify-content: center;
		}

		.print-image {
			max-width: 100%;
			max-height: 100%;
			object-fit: contain;
			border-radius: 8px;
		}

		.loading {
			text-align: center;
			color: #666;
		}

		@media print {
			body {
				-webkit-print-color-adjust: exact;
				print-color-adjust: exact;
			}
		}
	</style>
</head>
<body>
	<div class="print-container">
		<div class="loading">Loading image...</div>
		<img src="${imageUrl}" alt="Face Swapped Photo" class="print-image" style="display: none;" />
	</div>
	<script>
		console.log('Print window script loaded');
		const img = document.querySelector('.print-image');
		const loading = document.querySelector('.loading');

		img.onload = function() {
			console.log('Print window - Image loaded successfully');
			loading.style.display = 'none';
			img.style.display = 'block';

			setTimeout(function() {
				console.log('Print window - Triggering print dialog');
				window.print();
			}, 1000);
		};

		img.onerror = function() {
			console.error('Print window - Image failed to load');
			loading.textContent = 'Failed to load image';
		};

		setTimeout(function() {
			if (img.style.display === 'none') {
				console.log('Print window - Fallback: showing print dialog anyway');
				loading.style.display = 'none';
				img.style.display = 'block';
				window.print();
			}
		}, 5000);
	</script>
</body>
</html>`;
}
