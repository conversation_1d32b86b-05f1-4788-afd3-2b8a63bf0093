/**
 * Global user store for managing user information across the application
 * Uses Svelte 5 runes for reactive state management
 */

export interface UserInfo {
	gender: string | null;
	character: string | null;
	characterIndex: number | null;
	photo: File | null; // File object for form submission
	name: string | null;
	age: number | null;
	email: string | null;
	preferences: {
		theme: 'light' | 'dark' | 'auto';
		language: string;
	};
	// Add more fields as needed for future features
}

// Create reactive user state
function createUserStore() {
	// Initialize user data with default values
	let userData = $state<UserInfo>({
		gender: null,
		character: null,
		characterIndex: null,
		photo: null,
		name: null,
		age: null,
		email: null,
		preferences: {
			theme: 'auto',
			language: 'en'
		}
	});

	return {
		// Getter for the user data
		get data() {
			return userData;
		},

		// Update gender
		setGender(gender: string) {
			userData.gender = gender;
			console.log('Gender updated:', gender);
		},

		// Update character
		setCharacter(character: string) {
			userData.character = character;
			console.log('Character updated:', character);
		},

		// Update character index (for backend submission)
		setCharacterIndex(index: number) {
			userData.characterIndex = index + 1; // Store as 1-based index
			console.log('Character index updated:', index + 1);
		},

		// Update photo
		setPhoto(photo: File | null) {
			userData.photo = photo;
			console.log('Photo updated:', photo ? `${photo.name} (${photo.size} bytes)` : 'null');
		},

		// Update name
		setName(name: string) {
			userData.name = name;
			console.log('Name updated:', name);
		},

		// Update age
		setAge(age: number) {
			userData.age = age;
			console.log('Age updated:', age);
		},

		// Update email
		setEmail(email: string) {
			userData.email = email;
			console.log('Email updated:', email);
		},

		// Update preferences
		setPreferences(preferences: Partial<UserInfo['preferences']>) {
			userData.preferences = { ...userData.preferences, ...preferences };
			console.log('Preferences updated:', userData.preferences);
		},

		// Reset all user data
		reset() {
			const previousData = { ...userData };
			userData = {
				gender: null,
				character: null,
				characterIndex: null,
				photo: null,
				name: null,
				age: null,
				email: null,
				preferences: {
					theme: 'auto',
					language: 'en'
				}
			};
			console.log('User data reset:', {
				previous: previousData,
				current: userData,
				timestamp: new Date().toISOString()
			});
		},

		// Force reset (for development/debugging)
		forceReset() {
			this.reset();
			console.warn('Force reset triggered - all user data cleared');
		},

		// Get completion status
		get isComplete() {
			return !!(userData.gender && userData.name);
		},

		// Get progress percentage
		get progress() {
			const fields = [userData.gender, userData.character, userData.photo, userData.name, userData.age, userData.email];
			const completed = fields.filter(field => field !== null).length;
			return Math.round((completed / fields.length) * 100);
		},

		// Get current step information
		get currentStep() {
			if (!userData.gender) return { step: 1, total: 6, label: 'Gender Selection' };
			if (!userData.character) return { step: 2, total: 6, label: 'Character Selection' };
			if (!userData.photo) return { step: 3, total: 6, label: 'Photo Capture' };
			if (!userData.name) return { step: 4, total: 6, label: 'Personal Information' };
			if (!userData.age) return { step: 5, total: 6, label: 'Age Information' };
			if (!userData.email) return { step: 6, total: 6, label: 'Contact Information' };
			return { step: 6, total: 6, label: 'Complete' };
		},

		// Check if progress bar should be visible
		get showProgress() {
			return userData.gender !== null || userData.character !== null || userData.photo !== null || userData.name !== null || userData.age !== null || userData.email !== null;
		}
	};
}

// Export the singleton store instance
export const userStore = createUserStore();

// Export gender options for consistency (only Male and Female)
export const GENDER_OPTIONS = [
	{ value: 'male', label: 'Male', icon: '👨' },
	{ value: 'female', label: 'Female', icon: '👩' }
] as const;

export type GenderOption = typeof GENDER_OPTIONS[number]['value'];
