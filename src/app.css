@import 'tailwindcss';

/* Custom styles for the homepage */

/* Ensure the app takes full height */
html,
body {
	height: 100%;
	margin: 0;
	padding: 0;
}

/* Optimize for 1080x1920 resolution */
@media (max-width: 1080px) and (max-height: 1920px) {
	/* Larger font sizes for better visibility on 1080x1920 */
	.homepage-title {
		font-size: clamp(4rem, 12vw, 8rem) !important;
		line-height: 0.9;
	}

	.homepage-subtitle {
		font-size: clamp(1.5rem, 5vw, 3rem) !important;
		line-height: 1.2;
	}

	.homepage-button {
		font-size: clamp(1.5rem, 4vw, 2.5rem) !important;
		padding: clamp(1.5rem, 4vw, 2rem) clamp(3rem, 10vw, 5rem) !important;
	}

	/* Adjust spacing for larger components */
	.homepage-container {
		padding: 3rem 2rem;
	}
}

/* Enhanced button animations */
.start-button {
	position: relative;
	overflow: hidden;
	transform-style: preserve-3d;
	background-size: 200% 200%;
	animation: gradient-shift 3s ease infinite;
	border: 2px solid transparent;
	background-clip: padding-box;
}

.start-button::before {
	content: '';
	position: absolute;
	top: 0;
	left: -100%;
	width: 100%;
	height: 100%;
	background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
	transition: left 0.8s ease-in-out;
	z-index: 1;
}

.start-button:hover::before {
	left: 100%;
}

/* Additional glow effect on hover */
.start-button:hover {
	box-shadow:
		0 0 30px rgba(59, 130, 246, 0.6),
		0 0 60px rgba(147, 51, 234, 0.4),
		0 0 90px rgba(236, 72, 153, 0.3),
		inset 0 0 20px rgba(255, 255, 255, 0.1);
	transform: scale(1.1) translateY(-2px);
}

/* Magnetic effect */
.start-button:active {
	transform: scale(0.98) translateY(1px);
	box-shadow:
		0 0 15px rgba(59, 130, 246, 0.8),
		0 0 30px rgba(147, 51, 234, 0.6);
}

/* Video optimization */
video {
	object-fit: cover;
	object-position: center;
}

/* Custom shadow utilities */
.shadow-3xl {
	box-shadow: 0 35px 60px -12px rgba(0, 0, 0, 0.25);
}

/* Smooth scrolling and performance optimizations */
* {
	-webkit-font-smoothing: antialiased;
	-moz-osx-font-smoothing: grayscale;
}

/* Loading animation for video */
@keyframes pulse-glow {
	0%,
	100% {
		opacity: 1;
	}
	50% {
		opacity: 0.7;
	}
}

.video-loading {
	animation: pulse-glow 2s ease-in-out infinite;
}

/* Responsive adjustments for portrait orientation */
@media (orientation: portrait) {
	.homepage-container {
		padding: 3rem 2rem;
	}

	/* Specific optimizations for 1080x1920 portrait */
	@media (width: 1080px) and (height: 1920px) {
		.homepage-title {
			font-size: 10rem !important;
			margin-bottom: 2rem;
		}

		.homepage-subtitle {
			font-size: 3.5rem !important;
			margin-bottom: 3rem;
		}

		.start-button {
			font-size: 2.5rem !important;
			padding: 2rem 4rem !important;
		}

		/* Larger arrow icon for the specific resolution */
		.start-button svg {
			width: 3rem !important;
			height: 3rem !important;
		}

		/* Larger CTA text */
		.homepage-container p {
			font-size: 1.75rem !important;
		}
	}
}

/* High DPI display optimizations */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
	.start-button {
		border: 0.5px solid rgba(255, 255, 255, 0.1);
	}
}

/* Animation keyframes */
@keyframes fade-in {
	from {
		opacity: 0;
	}
	to {
		opacity: 1;
	}
}

@keyframes slide-up {
	from {
		opacity: 0;
		transform: translateY(30px);
	}
	to {
		opacity: 1;
		transform: translateY(0);
	}
}

/* Button Animation Keyframes */
@keyframes pulse-glow {
	0%,
	100% {
		box-shadow:
			0 0 20px rgba(59, 130, 246, 0.5),
			0 0 40px rgba(147, 51, 234, 0.3);
	}
	50% {
		box-shadow:
			0 0 30px rgba(59, 130, 246, 0.8),
			0 0 60px rgba(147, 51, 234, 0.5);
	}
}

@keyframes bounce-subtle {
	0%,
	100% {
		transform: translateY(0);
	}
	50% {
		transform: translateY(-2px);
	}
}

@keyframes float {
	0%,
	100% {
		transform: translateY(0px);
	}
	50% {
		transform: translateY(-4px);
	}
}

@keyframes pulse-border {
	0%,
	100% {
		border-color: rgba(59, 130, 246, 0.5);
		box-shadow: 0 0 10px rgba(59, 130, 246, 0.3);
	}
	33% {
		border-color: rgba(147, 51, 234, 0.5);
		box-shadow: 0 0 10px rgba(147, 51, 234, 0.3);
	}
	66% {
		border-color: rgba(236, 72, 153, 0.5);
		box-shadow: 0 0 10px rgba(236, 72, 153, 0.3);
	}
}

@keyframes arrow-wiggle {
	0%,
	100% {
		transform: translateX(0px);
	}
	25% {
		transform: translateX(3px);
	}
	75% {
		transform: translateX(-3px);
	}
}

@keyframes twinkle {
	0%,
	100% {
		opacity: 0;
		transform: scale(0);
	}
	50% {
		opacity: 1;
		transform: scale(1);
	}
}

@keyframes gradient-shift {
	0% {
		background-position: 0% 50%;
	}
	50% {
		background-position: 100% 50%;
	}
	100% {
		background-position: 0% 50%;
	}
}

/* Animation classes */
.animate-fade-in {
	animation: fade-in 1s ease-out forwards;
	opacity: 0;
}

.animate-slide-up {
	animation: slide-up 0.8s ease-out forwards;
	opacity: 0;
}

/* Button Animation Classes */
.animate-pulse-glow {
	animation: pulse-glow 2s ease-in-out infinite;
}

.animate-bounce-subtle {
	animation: bounce-subtle 2s ease-in-out infinite;
}

.animate-float {
	animation: float 3s ease-in-out infinite;
}

.animate-pulse-border {
	animation: pulse-border 2s ease-in-out infinite;
}

.animate-arrow-wiggle {
	animation: arrow-wiggle 2s ease-in-out infinite;
}

/* Gender selection page styles */
.gender-option {
	min-height: 200px;
	transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.gender-option:hover {
	transform: translateY(-4px) scale(1.02);
	box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
}

.gender-option:active {
	transform: translateY(-2px) scale(1.01);
}

/* Responsive adjustments for gender page (2 options layout) */
@media (max-width: 1080px) and (max-height: 1920px) {
	.gender-option {
		min-height: 300px;
		padding: 3rem;
	}

	.gender-option .text-6xl {
		font-size: 6rem;
	}

	.gender-option .text-2xl {
		font-size: 3rem;
	}
}

/* Specific optimizations for 1080x1920 portrait (2 options side by side) */
@media (orientation: portrait) and (width: 1080px) and (height: 1920px) {
	.gender-option {
		min-height: 350px;
		padding: 4rem;
		margin: 0 1rem;
	}

	.gender-option .text-6xl {
		font-size: 7rem;
	}

	.gender-option .text-2xl {
		font-size: 3.5rem;
	}

	/* Ensure proper spacing between the two options */
	.gender-option:first-child {
		margin-right: 2rem;
	}

	.gender-option:last-child {
		margin-left: 2rem;
	}
}

/* Character selection carousel styles */
.character-carousel {
	touch-action: pan-y;
	user-select: none;
	overflow: hidden;
}

.character-carousel img {
	pointer-events: none;
	user-select: none;
}

/* Smooth horizontal swipe animations */
.character-carousel .character-image {
	transition: transform 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.character-carousel.dragging .character-image {
	transition: none; /* Disable transition during drag for immediate feedback */
}

.animate-twinkle {
	animation: twinkle 1.5s ease-in-out infinite;
}

/* Staggered animation delays */
.animate-slide-up:nth-child(1) {
	animation-delay: 0.2s;
}

.animate-slide-up:nth-child(2) {
	animation-delay: 0.4s;
}

.animate-slide-up:nth-child(3) {
	animation-delay: 0.6s;
}
